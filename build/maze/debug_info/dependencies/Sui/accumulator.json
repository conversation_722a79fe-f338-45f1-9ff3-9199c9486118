{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_2c930c25f8d3/crates/sui-framework/packages/sui-framework/sources/accumulator.move", "definition_location": {"file_hash": [49, 85, 77, 211, 147, 183, 29, 18, 145, 158, 78, 117, 24, 31, 23, 48, 109, 186, 218, 206, 201, 108, 238, 32, 121, 73, 40, 63, 63, 16, 238, 219], "start": 87, "end": 98}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "accumulator"], "struct_map": {"0": {"definition_location": {"file_hash": [49, 85, 77, 211, 147, 183, 29, 18, 145, 158, 78, 117, 24, 31, 23, 48, 109, 186, 218, 206, 201, 108, 238, 32, 121, 73, 40, 63, 63, 16, 238, 219], "start": 150, "end": 165}, "type_parameters": [], "fields": [{"file_hash": [49, 85, 77, 211, 147, 183, 29, 18, 145, 158, 78, 117, 24, 31, 23, 48, 109, 186, 218, 206, 201, 108, 238, 32, 121, 73, 40, 63, 63, 16, 238, 219], "start": 180, "end": 182}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [49, 85, 77, 211, 147, 183, 29, 18, 145, 158, 78, 117, 24, 31, 23, 48, 109, 186, 218, 206, 201, 108, 238, 32, 121, 73, 40, 63, 63, 16, 238, 219], "start": 218, "end": 410}, "definition_location": {"file_hash": [49, 85, 77, 211, 147, 183, 29, 18, 145, 158, 78, 117, 24, 31, 23, 48, 109, 186, 218, 206, 201, 108, 238, 32, 121, 73, 40, 63, 63, 16, 238, 219], "start": 222, "end": 228}, "type_parameters": [], "parameters": [["ctx#0#0", {"file_hash": [49, 85, 77, 211, 147, 183, 29, 18, 145, 158, 78, 117, 24, 31, 23, 48, 109, 186, 218, 206, 201, 108, 238, 32, 121, 73, 40, 63, 63, 16, 238, 219], "start": 229, "end": 232}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [49, 85, 77, 211, 147, 183, 29, 18, 145, 158, 78, 117, 24, 31, 23, 48, 109, 186, 218, 206, 201, 108, 238, 32, 121, 73, 40, 63, 63, 16, 238, 219], "start": 260, "end": 263}, "1": {"file_hash": [49, 85, 77, 211, 147, 183, 29, 18, 145, 158, 78, 117, 24, 31, 23, 48, 109, 186, 218, 206, 201, 108, 238, 32, 121, 73, 40, 63, 63, 16, 238, 219], "start": 260, "end": 272}, "2": {"file_hash": [49, 85, 77, 211, 147, 183, 29, 18, 145, 158, 78, 117, 24, 31, 23, 48, 109, 186, 218, 206, 201, 108, 238, 32, 121, 73, 40, 63, 63, 16, 238, 219], "start": 276, "end": 280}, "3": {"file_hash": [49, 85, 77, 211, 147, 183, 29, 18, 145, 158, 78, 117, 24, 31, 23, 48, 109, 186, 218, 206, 201, 108, 238, 32, 121, 73, 40, 63, 63, 16, 238, 219], "start": 273, "end": 275}, "4": {"file_hash": [49, 85, 77, 211, 147, 183, 29, 18, 145, 158, 78, 117, 24, 31, 23, 48, 109, 186, 218, 206, 201, 108, 238, 32, 121, 73, 40, 63, 63, 16, 238, 219], "start": 252, "end": 300}, "6": {"file_hash": [49, 85, 77, 211, 147, 183, 29, 18, 145, 158, 78, 117, 24, 31, 23, 48, 109, 186, 218, 206, 201, 108, 238, 32, 121, 73, 40, 63, 63, 16, 238, 219], "start": 282, "end": 299}, "7": {"file_hash": [49, 85, 77, 211, 147, 183, 29, 18, 145, 158, 78, 117, 24, 31, 23, 48, 109, 186, 218, 206, 201, 108, 238, 32, 121, 73, 40, 63, 63, 16, 238, 219], "start": 252, "end": 300}, "8": {"file_hash": [49, 85, 77, 211, 147, 183, 29, 18, 145, 158, 78, 117, 24, 31, 23, 48, 109, 186, 218, 206, 201, 108, 238, 32, 121, 73, 40, 63, 63, 16, 238, 219], "start": 360, "end": 400}, "9": {"file_hash": [49, 85, 77, 211, 147, 183, 29, 18, 145, 158, 78, 117, 24, 31, 23, 48, 109, 186, 218, 206, 201, 108, 238, 32, 121, 73, 40, 63, 63, 16, 238, 219], "start": 330, "end": 407}, "10": {"file_hash": [49, 85, 77, 211, 147, 183, 29, 18, 145, 158, 78, 117, 24, 31, 23, 48, 109, 186, 218, 206, 201, 108, 238, 32, 121, 73, 40, 63, 63, 16, 238, 219], "start": 307, "end": 408}}, "is_native": false}}, "constant_map": {"ENotSystemAddress": 0}}