{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_2c930c25f8d3/crates/sui-framework/packages/sui-framework/sources/crypto/ed25519.move", "definition_location": {"file_hash": [168, 198, 13, 32, 20, 214, 192, 94, 134, 88, 205, 210, 135, 0, 82, 97, 87, 60, 112, 88, 113, 43, 246, 158, 52, 98, 140, 76, 136, 8, 129, 165], "start": 87, "end": 94}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "ed25519"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [168, 198, 13, 32, 20, 214, 192, 94, 134, 88, 205, 210, 135, 0, 82, 97, 87, 60, 112, 88, 113, 43, 246, 158, 52, 98, 140, 76, 136, 8, 129, 165], "start": 463, "end": 584}, "definition_location": {"file_hash": [168, 198, 13, 32, 20, 214, 192, 94, 134, 88, 205, 210, 135, 0, 82, 97, 87, 60, 112, 88, 113, 43, 246, 158, 52, 98, 140, 76, 136, 8, 129, 165], "start": 481, "end": 495}, "type_parameters": [], "parameters": [["signature#0#0", {"file_hash": [168, 198, 13, 32, 20, 214, 192, 94, 134, 88, 205, 210, 135, 0, 82, 97, 87, 60, 112, 88, 113, 43, 246, 158, 52, 98, 140, 76, 136, 8, 129, 165], "start": 501, "end": 510}], ["public_key#0#0", {"file_hash": [168, 198, 13, 32, 20, 214, 192, 94, 134, 88, 205, 210, 135, 0, 82, 97, 87, 60, 112, 88, 113, 43, 246, 158, 52, 98, 140, 76, 136, 8, 129, 165], "start": 529, "end": 539}], ["msg#0#0", {"file_hash": [168, 198, 13, 32, 20, 214, 192, 94, 134, 88, 205, 210, 135, 0, 82, 97, 87, 60, 112, 88, 113, 43, 246, 158, 52, 98, 140, 76, 136, 8, 129, 165], "start": 558, "end": 561}]], "returns": [{"file_hash": [168, 198, 13, 32, 20, 214, 192, 94, 134, 88, 205, 210, 135, 0, 82, 97, 87, 60, 112, 88, 113, 43, 246, 158, 52, 98, 140, 76, 136, 8, 129, 165], "start": 579, "end": 583}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {}}