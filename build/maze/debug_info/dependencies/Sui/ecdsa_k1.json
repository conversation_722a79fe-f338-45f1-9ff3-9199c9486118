{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_2c930c25f8d3/crates/sui-framework/packages/sui-framework/sources/crypto/ecdsa_k1.move", "definition_location": {"file_hash": [102, 177, 193, 42, 161, 180, 43, 85, 135, 223, 147, 205, 67, 152, 140, 203, 145, 135, 131, 206, 242, 79, 5, 207, 221, 62, 142, 121, 15, 31, 198, 180], "start": 87, "end": 95}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "ecdsa_k1"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [102, 177, 193, 42, 161, 180, 43, 85, 135, 223, 147, 205, 67, 152, 140, 203, 145, 135, 131, 206, 242, 79, 5, 207, 221, 62, 142, 121, 15, 31, 198, 180], "start": 1691, "end": 1808}, "definition_location": {"file_hash": [102, 177, 193, 42, 161, 180, 43, 85, 135, 223, 147, 205, 67, 152, 140, 203, 145, 135, 131, 206, 242, 79, 5, 207, 221, 62, 142, 121, 15, 31, 198, 180], "start": 1709, "end": 1728}, "type_parameters": [], "parameters": [["signature#0#0", {"file_hash": [102, 177, 193, 42, 161, 180, 43, 85, 135, 223, 147, 205, 67, 152, 140, 203, 145, 135, 131, 206, 242, 79, 5, 207, 221, 62, 142, 121, 15, 31, 198, 180], "start": 1734, "end": 1743}], ["msg#0#0", {"file_hash": [102, 177, 193, 42, 161, 180, 43, 85, 135, 223, 147, 205, 67, 152, 140, 203, 145, 135, 131, 206, 242, 79, 5, 207, 221, 62, 142, 121, 15, 31, 198, 180], "start": 1762, "end": 1765}], ["hash#0#0", {"file_hash": [102, 177, 193, 42, 161, 180, 43, 85, 135, 223, 147, 205, 67, 152, 140, 203, 145, 135, 131, 206, 242, 79, 5, 207, 221, 62, 142, 121, 15, 31, 198, 180], "start": 1784, "end": 1788}]], "returns": [{"file_hash": [102, 177, 193, 42, 161, 180, 43, 85, 135, 223, 147, 205, 67, 152, 140, 203, 145, 135, 131, 206, 242, 79, 5, 207, 221, 62, 142, 121, 15, 31, 198, 180], "start": 1797, "end": 1807}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "1": {"location": {"file_hash": [102, 177, 193, 42, 161, 180, 43, 85, 135, 223, 147, 205, 67, 152, 140, 203, 145, 135, 131, 206, 242, 79, 5, 207, 221, 62, 142, 121, 15, 31, 198, 180], "start": 2067, "end": 2136}, "definition_location": {"file_hash": [102, 177, 193, 42, 161, 180, 43, 85, 135, 223, 147, 205, 67, 152, 140, 203, 145, 135, 131, 206, 242, 79, 5, 207, 221, 62, 142, 121, 15, 31, 198, 180], "start": 2085, "end": 2102}, "type_parameters": [], "parameters": [["pubkey#0#0", {"file_hash": [102, 177, 193, 42, 161, 180, 43, 85, 135, 223, 147, 205, 67, 152, 140, 203, 145, 135, 131, 206, 242, 79, 5, 207, 221, 62, 142, 121, 15, 31, 198, 180], "start": 2103, "end": 2109}]], "returns": [{"file_hash": [102, 177, 193, 42, 161, 180, 43, 85, 135, 223, 147, 205, 67, 152, 140, 203, 145, 135, 131, 206, 242, 79, 5, 207, 221, 62, 142, 121, 15, 31, 198, 180], "start": 2125, "end": 2135}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "2": {"location": {"file_hash": [102, 177, 193, 42, 161, 180, 43, 85, 135, 223, 147, 205, 67, 152, 140, 203, 145, 135, 131, 206, 242, 79, 5, 207, 221, 62, 142, 121, 15, 31, 198, 180], "start": 2826, "end": 2963}, "definition_location": {"file_hash": [102, 177, 193, 42, 161, 180, 43, 85, 135, 223, 147, 205, 67, 152, 140, 203, 145, 135, 131, 206, 242, 79, 5, 207, 221, 62, 142, 121, 15, 31, 198, 180], "start": 2844, "end": 2860}, "type_parameters": [], "parameters": [["signature#0#0", {"file_hash": [102, 177, 193, 42, 161, 180, 43, 85, 135, 223, 147, 205, 67, 152, 140, 203, 145, 135, 131, 206, 242, 79, 5, 207, 221, 62, 142, 121, 15, 31, 198, 180], "start": 2866, "end": 2875}], ["public_key#0#0", {"file_hash": [102, 177, 193, 42, 161, 180, 43, 85, 135, 223, 147, 205, 67, 152, 140, 203, 145, 135, 131, 206, 242, 79, 5, 207, 221, 62, 142, 121, 15, 31, 198, 180], "start": 2894, "end": 2904}], ["msg#0#0", {"file_hash": [102, 177, 193, 42, 161, 180, 43, 85, 135, 223, 147, 205, 67, 152, 140, 203, 145, 135, 131, 206, 242, 79, 5, 207, 221, 62, 142, 121, 15, 31, 198, 180], "start": 2923, "end": 2926}], ["hash#0#0", {"file_hash": [102, 177, 193, 42, 161, 180, 43, 85, 135, 223, 147, 205, 67, 152, 140, 203, 145, 135, 131, 206, 242, 79, 5, 207, 221, 62, 142, 121, 15, 31, 198, 180], "start": 2945, "end": 2949}]], "returns": [{"file_hash": [102, 177, 193, 42, 161, 180, 43, 85, 135, 223, 147, 205, 67, 152, 140, 203, 145, 135, 131, 206, 242, 79, 5, 207, 221, 62, 142, 121, 15, 31, 198, 180], "start": 2958, "end": 2962}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {"EFailToRecoverPubKey": 0, "EInvalidPubKey": 2, "EInvalidSignature": 1, "KECCAK256": 3, "SHA256": 4}}