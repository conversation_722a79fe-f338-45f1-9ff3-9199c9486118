{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_2c930c25f8d3/crates/sui-framework/packages/sui-framework/sources/math.move", "definition_location": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 270, "end": 274}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "math"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 321, "end": 373}, "definition_location": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 332, "end": 335}, "type_parameters": [], "parameters": [["x#0#0", {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 336, "end": 337}], ["y#0#0", {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 344, "end": 345}]], "returns": [{"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 353, "end": 356}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 363, "end": 364}, "1": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 369, "end": 370}, "2": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 363, "end": 371}}, "is_native": false}, "1": {"location": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 419, "end": 471}, "definition_location": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 430, "end": 433}, "type_parameters": [], "parameters": [["x#0#0", {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 434, "end": 435}], ["y#0#0", {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 442, "end": 443}]], "returns": [{"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 451, "end": 454}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 461, "end": 462}, "1": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 467, "end": 468}, "2": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 461, "end": 469}}, "is_native": false}, "2": {"location": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 518, "end": 572}, "definition_location": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 529, "end": 533}, "type_parameters": [], "parameters": [["x#0#0", {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 534, "end": 535}], ["y#0#0", {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 542, "end": 543}]], "returns": [{"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 551, "end": 554}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 561, "end": 562}, "1": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 568, "end": 569}, "2": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 561, "end": 570}}, "is_native": false}, "3": {"location": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 618, "end": 689}, "definition_location": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 629, "end": 632}, "type_parameters": [], "parameters": [["base#0#0", {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 633, "end": 637}], ["exponent#0#0", {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 644, "end": 652}]], "returns": [{"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 659, "end": 662}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 669, "end": 673}, "1": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 678, "end": 686}, "2": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 669, "end": 687}}, "is_native": false}, "4": {"location": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 736, "end": 781}, "definition_location": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 747, "end": 751}, "type_parameters": [], "parameters": [["x#0#0", {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 752, "end": 753}]], "returns": [{"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 761, "end": 764}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 771, "end": 772}, "1": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 771, "end": 779}}, "is_native": false}, "5": {"location": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 829, "end": 881}, "definition_location": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 840, "end": 849}, "type_parameters": [], "parameters": [["x#0#0", {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 850, "end": 851}]], "returns": [{"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 860, "end": 864}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 871, "end": 872}, "1": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 871, "end": 879}}, "is_native": false}, "6": {"location": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 943, "end": 1027}, "definition_location": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 954, "end": 973}, "type_parameters": [], "parameters": [["x#0#0", {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 974, "end": 975}], ["y#0#0", {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 982, "end": 983}]], "returns": [{"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 991, "end": 994}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 1001, "end": 1002}, "1": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 1023, "end": 1024}, "2": {"file_hash": [219, 98, 141, 53, 106, 71, 127, 240, 88, 241, 6, 103, 123, 37, 162, 241, 71, 251, 181, 165, 63, 86, 231, 137, 21, 140, 102, 240, 84, 24, 64, 236], "start": 1001, "end": 1025}}, "is_native": false}}, "constant_map": {}}