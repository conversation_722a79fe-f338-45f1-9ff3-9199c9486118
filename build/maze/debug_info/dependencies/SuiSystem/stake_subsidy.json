{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_2c930c25f8d3/crates/sui-framework/packages/sui-system/sources/stake_subsidy.move", "definition_location": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 94, "end": 107}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000003", "stake_subsidy"], "struct_map": {"0": {"definition_location": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 291, "end": 303}, "type_parameters": [], "fields": [{"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 408, "end": 415}, {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 511, "end": 531}, {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 665, "end": 692}, {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 783, "end": 810}, {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 940, "end": 967}, {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1034, "end": 1046}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1056, "end": 1690}, "definition_location": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1076, "end": 1082}, "type_parameters": [], "parameters": [["balance#0#0", {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1088, "end": 1095}], ["initial_distribution_amount#0#0", {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1115, "end": 1142}], ["stake_subsidy_period_length#0#0", {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1153, "end": 1180}], ["stake_subsidy_decrease_rate#0#0", {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1191, "end": 1218}], ["ctx#0#0", {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1229, "end": 1232}]], "returns": [{"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1253, "end": 1265}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1328, "end": 1355}, "1": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1359, "end": 1382}, "2": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1359, "end": 1389}, "3": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1356, "end": 1358}, "4": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1311, "end": 1434}, "8": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1399, "end": 1427}, "9": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1311, "end": 1434}, "10": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1464, "end": 1471}, "11": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1503, "end": 1504}, "12": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1543, "end": 1570}, "13": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1580, "end": 1607}, "14": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1617, "end": 1644}, "15": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1677, "end": 1680}, "16": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1668, "end": 1681}, "17": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1441, "end": 1688}}, "is_native": false}, "1": {"location": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1763, "end": 2704}, "definition_location": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1783, "end": 1796}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1797, "end": 1801}]], "returns": [{"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 1823, "end": 1835}], "locals": [["decrease_amount#1#0", {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2406, "end": 2421}], ["stake_subsidy#1#0", {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2134, "end": 2147}], ["to_withdraw#1#0", {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2005, "end": 2016}]], "nops": {}, "code_map": {"0": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2019, "end": 2023}, "1": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2019, "end": 2051}, "3": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2056, "end": 2060}, "4": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2056, "end": 2068}, "5": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2056, "end": 2076}, "6": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2019, "end": 2077}, "7": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2005, "end": 2016}, "8": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2150, "end": 2154}, "9": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2150, "end": 2162}, "10": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2169, "end": 2180}, "11": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2150, "end": 2181}, "12": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2134, "end": 2147}, "13": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2215, "end": 2219}, "14": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2215, "end": 2240}, "16": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2243, "end": 2244}, "17": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2241, "end": 2242}, "18": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2187, "end": 2191}, "19": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2187, "end": 2212}, "20": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2187, "end": 2244}, "21": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2325, "end": 2329}, "22": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2325, "end": 2350}, "24": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2353, "end": 2357}, "25": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2353, "end": 2385}, "27": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2351, "end": 2352}, "28": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2389, "end": 2390}, "29": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2386, "end": 2388}, "30": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2321, "end": 2682}, "31": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2436, "end": 2440}, "32": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2436, "end": 2468}, "34": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2436, "end": 2476}, "35": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2492, "end": 2496}, "36": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2492, "end": 2524}, "38": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2492, "end": 2532}, "39": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2489, "end": 2490}, "40": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2536, "end": 2559}, "41": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2534, "end": 2535}, "42": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2406, "end": 2421}, "43": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2617, "end": 2621}, "44": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2617, "end": 2649}, "46": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2653, "end": 2668}, "47": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2653, "end": 2675}, "48": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2650, "end": 2651}, "49": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2570, "end": 2574}, "50": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2570, "end": 2602}, "51": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2570, "end": 2676}, "52": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2321, "end": 2682}, "55": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2689, "end": 2702}}, "is_native": false}, "2": {"location": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2791, "end": 2923}, "definition_location": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2802, "end": 2830}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2831, "end": 2835}]], "returns": [{"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2853, "end": 2856}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2863, "end": 2867}, "1": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2863, "end": 2895}, "3": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2900, "end": 2904}, "4": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2900, "end": 2912}, "5": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2900, "end": 2920}, "6": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2863, "end": 2921}}, "is_native": false}, "3": {"location": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 2985, "end": 3089}, "definition_location": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 3005, "end": 3029}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 3030, "end": 3034}]], "returns": [{"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 3052, "end": 3055}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 3062, "end": 3066}, "1": {"file_hash": [65, 23, 192, 113, 32, 221, 56, 187, 83, 68, 136, 54, 88, 254, 5, 101, 116, 223, 39, 210, 139, 135, 47, 100, 176, 35, 202, 114, 65, 166, 61, 232], "start": 3062, "end": 3087}}, "is_native": false}}, "constant_map": {"BASIS_POINT_DENOMINATOR": 1, "ESubsidyDecreaseRateTooLarge": 0}}