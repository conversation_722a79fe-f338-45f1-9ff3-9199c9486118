{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_2c930c25f8d3/crates/sui-framework/packages/sui-system/sources/sui_system.move", "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 3408, "end": 3418}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000003", "sui_system"], "struct_map": {"0": {"definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4065, "end": 4079}, "type_parameters": [], "fields": [{"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4094, "end": 4096}, {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4107, "end": 4114}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4367, "end": 5095}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4387, "end": 4393}, "type_parameters": [], "parameters": [["id#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4399, "end": 4401}], ["validators#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4412, "end": 4422}], ["storage_fund#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4447, "end": 4459}], ["protocol_version#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4479, "end": 4495}], ["epoch_start_timestamp_ms#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4506, "end": 4530}], ["parameters#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4541, "end": 4551}], ["stake_subsidy#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4575, "end": 4588}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4608, "end": 4611}]], "returns": [], "locals": [["self#1#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4939, "end": 4943}], ["system_state#1#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4641, "end": 4653}], ["version#1#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4861, "end": 4868}]], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4696, "end": 4706}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4716, "end": 4728}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4738, "end": 4754}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4764, "end": 4788}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4798, "end": 4808}, "5": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4818, "end": 4831}, "6": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4841, "end": 4844}, "7": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4656, "end": 4851}, "8": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4641, "end": 4653}, "9": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4871, "end": 4925}, "10": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4861, "end": 4868}, "11": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4971, "end": 4973}, "12": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4983, "end": 4990}, "13": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4946, "end": 4997}, "14": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 4935, "end": 4943}, "15": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 5027, "end": 5034}, "16": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 5022, "end": 5034}, "17": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 5036, "end": 5043}, "18": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 5045, "end": 5057}, "19": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 5003, "end": 5058}, "20": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 5087, "end": 5091}, "21": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 5064, "end": 5092}, "22": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 5092, "end": 5093}}, "is_native": false}, "1": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 5693, "end": 6715}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 5710, "end": 5741}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 5747, "end": 5754}], ["pubkey_bytes#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 5781, "end": 5793}], ["network_pubkey_bytes#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 5811, "end": 5831}], ["worker_pubkey_bytes#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 5849, "end": 5868}], ["proof_of_possession#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 5886, "end": 5905}], ["name#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 5923, "end": 5927}], ["description#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 5945, "end": 5956}], ["image_url#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 5974, "end": 5983}], ["project_url#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6001, "end": 6012}], ["net_address#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6030, "end": 6041}], ["p2p_address#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6059, "end": 6070}], ["primary_address#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6088, "end": 6103}], ["worker_address#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6121, "end": 6135}], ["gas_price#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6153, "end": 6162}], ["commission_rate#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6173, "end": 6188}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6199, "end": 6202}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6228, "end": 6235}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6228, "end": 6268}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6323, "end": 6335}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6349, "end": 6369}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6383, "end": 6402}, "5": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6416, "end": 6435}, "6": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6449, "end": 6453}, "7": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6467, "end": 6478}, "8": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6492, "end": 6501}, "9": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6515, "end": 6526}, "10": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6540, "end": 6551}, "11": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6565, "end": 6576}, "12": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6590, "end": 6605}, "13": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6619, "end": 6633}, "14": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6647, "end": 6656}, "15": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6670, "end": 6685}, "16": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6699, "end": 6702}, "17": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6228, "end": 6713}}, "is_native": false}, "2": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6853, "end": 7046}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6870, "end": 6904}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6910, "end": 6917}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6944, "end": 6947}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6973, "end": 6980}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6973, "end": 7004}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 7040, "end": 7043}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 6973, "end": 7044}}, "is_native": false}, "3": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 7412, "end": 7568}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 7429, "end": 7450}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 7451, "end": 7458}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 7481, "end": 7484}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 7508, "end": 7515}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 7508, "end": 7539}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 7562, "end": 7565}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 7508, "end": 7566}}, "is_native": false}, "4": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 7878, "end": 8040}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 7895, "end": 7919}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 7920, "end": 7927}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 7950, "end": 7953}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 7977, "end": 7984}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 7977, "end": 8008}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 8034, "end": 8037}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 7977, "end": 8038}}, "is_native": false}, "5": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 8200, "end": 8424}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 8217, "end": 8238}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 8244, "end": 8251}], ["cap#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 8278, "end": 8281}], ["new_gas_price#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 8321, "end": 8334}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 8349, "end": 8356}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 8349, "end": 8380}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 8403, "end": 8406}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 8408, "end": 8421}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 8349, "end": 8422}}, "is_native": false}, "6": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 8504, "end": 8752}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 8521, "end": 8554}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 8560, "end": 8567}], ["cap#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 8594, "end": 8597}], ["new_gas_price#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 8637, "end": 8650}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 8665, "end": 8672}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 8665, "end": 8696}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 8731, "end": 8734}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 8736, "end": 8749}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 8665, "end": 8750}}, "is_native": false}, "7": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 8866, "end": 9096}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 8883, "end": 8910}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 8916, "end": 8923}], ["new_commission_rate#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 8950, "end": 8969}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 8980, "end": 8983}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9009, "end": 9016}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9009, "end": 9040}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9069, "end": 9088}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9090, "end": 9093}, "5": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9009, "end": 9094}}, "is_native": false}, "8": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9182, "end": 9454}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9199, "end": 9238}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9244, "end": 9251}], ["new_commission_rate#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9278, "end": 9297}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9308, "end": 9311}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9337, "end": 9344}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9337, "end": 9377}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9427, "end": 9446}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9448, "end": 9451}, "5": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9337, "end": 9452}}, "is_native": false}, "9": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9501, "end": 9802}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9518, "end": 9535}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9541, "end": 9548}], ["stake#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9575, "end": 9580}], ["validator_address#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9597, "end": 9614}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9629, "end": 9632}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9703, "end": 9710}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9712, "end": 9717}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9719, "end": 9736}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9738, "end": 9741}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9675, "end": 9742}, "5": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9786, "end": 9789}, "7": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9786, "end": 9798}, "8": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9748, "end": 9799}, "9": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9799, "end": 9800}}, "is_native": false}, "10": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9925, "end": 10179}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9936, "end": 9963}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 9969, "end": 9976}], ["stake#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10003, "end": 10008}], ["validator_address#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10025, "end": 10042}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10057, "end": 10060}]], "returns": [{"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10081, "end": 10090}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10097, "end": 10104}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10097, "end": 10128}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10147, "end": 10152}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10154, "end": 10171}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10173, "end": 10176}, "5": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10097, "end": 10177}}, "is_native": false}, "11": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10247, "end": 10661}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10264, "end": 10290}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10296, "end": 10303}], ["stakes#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10330, "end": 10336}], ["stake_amount#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10361, "end": 10373}], ["validator_address#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10400, "end": 10417}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10432, "end": 10435}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10478, "end": 10485}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10478, "end": 10518}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10555, "end": 10561}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10563, "end": 10575}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10577, "end": 10594}, "5": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10596, "end": 10599}, "6": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10478, "end": 10600}, "7": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10645, "end": 10648}, "9": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10645, "end": 10657}, "10": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10607, "end": 10658}, "11": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10658, "end": 10659}}, "is_native": false}, "12": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10715, "end": 11009}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10732, "end": 10754}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10760, "end": 10767}], ["staked_sui#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10794, "end": 10804}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10821, "end": 10824}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10872, "end": 10879}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10913, "end": 10923}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10925, "end": 10928}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10872, "end": 10929}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10987, "end": 10990}, "5": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10961, "end": 10991}, "6": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10993, "end": 10996}, "8": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10993, "end": 11005}, "9": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 10935, "end": 11006}, "10": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11006, "end": 11007}}, "is_native": false}, "13": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11066, "end": 11303}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11077, "end": 11107}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11113, "end": 11120}], ["staked_sui#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11147, "end": 11157}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11174, "end": 11177}]], "returns": [{"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11198, "end": 11215}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11222, "end": 11229}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11222, "end": 11253}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11285, "end": 11295}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11297, "end": 11300}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11222, "end": 11301}}, "is_native": false}, "14": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11360, "end": 11606}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11371, "end": 11397}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11403, "end": 11410}], ["fungible_staked_sui#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11437, "end": 11456}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11481, "end": 11484}]], "returns": [{"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11501, "end": 11513}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11520, "end": 11527}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11520, "end": 11551}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11579, "end": 11598}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11600, "end": 11603}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11520, "end": 11604}}, "is_native": false}, "15": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11731, "end": 11957}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11742, "end": 11774}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11780, "end": 11787}], ["staked_sui#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11814, "end": 11824}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11841, "end": 11844}]], "returns": [{"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11865, "end": 11877}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11884, "end": 11891}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11884, "end": 11915}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11939, "end": 11949}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11951, "end": 11954}, "5": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 11884, "end": 11955}}, "is_native": false}, "16": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 12288, "end": 12506}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 12305, "end": 12321}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 12327, "end": 12334}], ["cap#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 12361, "end": 12364}], ["reportee_addr#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 12404, "end": 12417}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 12436, "end": 12443}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 12436, "end": 12467}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 12485, "end": 12488}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 12490, "end": 12503}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 12436, "end": 12504}}, "is_native": false}, "17": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 12713, "end": 12941}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 12730, "end": 12751}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 12757, "end": 12764}], ["cap#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 12791, "end": 12794}], ["reportee_addr#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 12834, "end": 12847}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 12866, "end": 12873}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 12866, "end": 12897}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 12920, "end": 12923}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 12925, "end": 12938}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 12866, "end": 12939}}, "is_native": false}, "18": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13137, "end": 13285}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13154, "end": 13174}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13175, "end": 13179}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13202, "end": 13205}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13229, "end": 13233}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13229, "end": 13257}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13279, "end": 13282}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13229, "end": 13283}}, "is_native": false}, "19": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13318, "end": 13503}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13335, "end": 13356}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13362, "end": 13366}], ["name#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13393, "end": 13397}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13415, "end": 13418}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13440, "end": 13444}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13440, "end": 13468}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13491, "end": 13495}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13497, "end": 13500}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13440, "end": 13501}}, "is_native": false}, "20": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13542, "end": 13755}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13559, "end": 13587}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13593, "end": 13597}], ["description#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13624, "end": 13635}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13653, "end": 13656}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13678, "end": 13682}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13678, "end": 13706}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13736, "end": 13747}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13749, "end": 13752}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13678, "end": 13753}}, "is_native": false}, "21": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13792, "end": 13997}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13809, "end": 13835}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13841, "end": 13845}], ["image_url#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13872, "end": 13881}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13899, "end": 13902}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13924, "end": 13928}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13924, "end": 13952}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13980, "end": 13989}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13991, "end": 13994}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 13924, "end": 13995}}, "is_native": false}, "22": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14036, "end": 14249}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14053, "end": 14081}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14087, "end": 14091}], ["project_url#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14118, "end": 14129}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14147, "end": 14150}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14172, "end": 14176}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14172, "end": 14200}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14230, "end": 14241}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14243, "end": 14246}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14172, "end": 14247}}, "is_native": false}, "23": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14361, "end": 14612}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14378, "end": 14421}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14427, "end": 14431}], ["network_address#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14458, "end": 14473}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14491, "end": 14494}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14516, "end": 14520}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14516, "end": 14544}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14589, "end": 14604}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14606, "end": 14609}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14516, "end": 14610}}, "is_native": false}, "24": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14664, "end": 14913}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14681, "end": 14723}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14729, "end": 14733}], ["network_address#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14760, "end": 14775}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14793, "end": 14796}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14818, "end": 14822}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14818, "end": 14846}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14890, "end": 14905}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14907, "end": 14910}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 14818, "end": 14911}}, "is_native": false}, "25": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15021, "end": 15256}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15038, "end": 15077}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15083, "end": 15087}], ["p2p_address#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15114, "end": 15125}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15143, "end": 15146}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15168, "end": 15172}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15168, "end": 15196}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15237, "end": 15248}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15250, "end": 15253}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15168, "end": 15254}}, "is_native": false}, "26": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15304, "end": 15537}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15321, "end": 15359}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15365, "end": 15369}], ["p2p_address#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15396, "end": 15407}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15425, "end": 15428}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15450, "end": 15454}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15450, "end": 15478}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15518, "end": 15529}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15531, "end": 15534}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15450, "end": 15535}}, "is_native": false}, "27": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15657, "end": 15908}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15674, "end": 15717}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15723, "end": 15727}], ["primary_address#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15754, "end": 15769}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15787, "end": 15790}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15812, "end": 15816}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15812, "end": 15840}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15885, "end": 15900}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15902, "end": 15905}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15812, "end": 15906}}, "is_native": false}, "28": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15968, "end": 16217}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 15985, "end": 16027}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16033, "end": 16037}], ["primary_address#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16064, "end": 16079}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16097, "end": 16100}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16122, "end": 16126}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16122, "end": 16150}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16194, "end": 16209}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16211, "end": 16214}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16122, "end": 16215}}, "is_native": false}, "29": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16336, "end": 16583}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16353, "end": 16395}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16401, "end": 16405}], ["worker_address#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16432, "end": 16446}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16464, "end": 16467}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16489, "end": 16493}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16489, "end": 16517}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16561, "end": 16575}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16577, "end": 16580}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16489, "end": 16581}}, "is_native": false}, "30": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16642, "end": 16887}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16659, "end": 16700}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16706, "end": 16710}], ["worker_address#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16737, "end": 16751}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16769, "end": 16772}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16794, "end": 16798}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16794, "end": 16822}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16865, "end": 16879}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16881, "end": 16884}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 16794, "end": 16885}}, "is_native": false}, "31": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17034, "end": 17361}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17051, "end": 17094}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17100, "end": 17104}], ["protocol_pubkey#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17131, "end": 17146}], ["proof_of_possession#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17164, "end": 17183}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17201, "end": 17204}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17226, "end": 17230}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17226, "end": 17263}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17317, "end": 17332}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17334, "end": 17353}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17355, "end": 17358}, "5": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17226, "end": 17359}}, "is_native": false}, "32": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17448, "end": 17773}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17465, "end": 17507}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17513, "end": 17517}], ["protocol_pubkey#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17544, "end": 17559}], ["proof_of_possession#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17577, "end": 17596}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17614, "end": 17617}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17639, "end": 17643}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17639, "end": 17676}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17729, "end": 17744}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17746, "end": 17765}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17767, "end": 17770}, "5": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17639, "end": 17771}}, "is_native": false}, "33": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17894, "end": 18137}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17911, "end": 17952}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17958, "end": 17962}], ["worker_pubkey#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 17989, "end": 18002}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18020, "end": 18023}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18045, "end": 18049}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18045, "end": 18073}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18116, "end": 18129}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18131, "end": 18134}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18045, "end": 18135}}, "is_native": false}, "34": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18198, "end": 18439}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18215, "end": 18255}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18261, "end": 18265}], ["worker_pubkey#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18292, "end": 18305}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18323, "end": 18326}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18348, "end": 18352}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18348, "end": 18376}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18418, "end": 18431}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18433, "end": 18436}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18348, "end": 18437}}, "is_native": false}, "35": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18561, "end": 18808}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18578, "end": 18620}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18626, "end": 18630}], ["network_pubkey#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18657, "end": 18671}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18689, "end": 18692}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18714, "end": 18718}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18714, "end": 18742}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18786, "end": 18800}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18802, "end": 18805}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18714, "end": 18806}}, "is_native": false}, "36": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18870, "end": 19115}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18887, "end": 18928}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18934, "end": 18938}], ["network_pubkey#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18965, "end": 18979}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 18997, "end": 19000}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 19022, "end": 19026}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 19022, "end": 19050}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 19093, "end": 19107}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 19109, "end": 19112}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 19022, "end": 19113}}, "is_native": false}, "37": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 19117, "end": 19287}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 19128, "end": 19156}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 19157, "end": 19164}], ["pool_id#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 19187, "end": 19194}]], "returns": [{"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 19202, "end": 19209}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 19216, "end": 19223}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 19216, "end": 19247}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 19277, "end": 19284}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 19216, "end": 19285}}, "is_native": false}, "38": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 19393, "end": 19583}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 19404, "end": 19423}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 19429, "end": 19436}], ["pool_id#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 19463, "end": 19470}]], "returns": [{"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 19480, "end": 19514}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 19521, "end": 19528}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 19521, "end": 19552}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 19573, "end": 19580}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 19521, "end": 19581}}, "is_native": false}, "39": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 20123, "end": 20276}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 20134, "end": 20160}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 20161, "end": 20168}]], "returns": [{"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 20192, "end": 20207}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 20214, "end": 20221}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 20214, "end": 20245}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 20214, "end": 20274}}, "is_native": false}, "40": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 20698, "end": 21837}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 20702, "end": 20715}, "type_parameters": [], "parameters": [["storage_reward#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 20721, "end": 20735}], ["computation_reward#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 20755, "end": 20773}], ["wrapper#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 20793, "end": 20800}], ["new_epoch#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 20827, "end": 20836}], ["next_protocol_version#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 20847, "end": 20868}], ["storage_rebate#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 20879, "end": 20893}], ["non_refundable_storage_fee#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 20904, "end": 20930}], ["storage_fund_reinvest_rate#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 20941, "end": 20967}], ["reward_slashing_rate#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21073, "end": 21093}], ["epoch_start_timestamp_ms#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21167, "end": 21191}], ["ctx#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21234, "end": 21237}]], "returns": [{"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21258, "end": 21270}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21358, "end": 21361}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21358, "end": 21370}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21374, "end": 21378}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21371, "end": 21373}, "5": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21350, "end": 21398}, "11": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21380, "end": 21397}, "12": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21350, "end": 21398}, "13": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21425, "end": 21432}, "14": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21425, "end": 21465}, "15": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21502, "end": 21511}, "16": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21525, "end": 21546}, "17": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21560, "end": 21574}, "18": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21588, "end": 21606}, "19": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21620, "end": 21634}, "20": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21648, "end": 21674}, "21": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21688, "end": 21714}, "22": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21728, "end": 21748}, "23": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21762, "end": 21786}, "24": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21800, "end": 21803}, "25": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21425, "end": 21814}, "26": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21821, "end": 21835}}, "is_native": false}, "41": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21839, "end": 21950}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21843, "end": 21860}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21861, "end": 21865}]], "returns": [{"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21889, "end": 21911}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21943, "end": 21947}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21918, "end": 21948}}, "is_native": false}, "42": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21952, "end": 22071}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21956, "end": 21977}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 21978, "end": 21982}]], "returns": [{"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22006, "end": 22032}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22064, "end": 22068}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22039, "end": 22069}}, "is_native": false}, "43": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22073, "end": 22615}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22077, "end": 22101}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22102, "end": 22106}]], "returns": [{"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22130, "end": 22156}], "locals": [["inner#1#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22411, "end": 22416}], ["v2#1#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22289, "end": 22291}]], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22167, "end": 22171}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22167, "end": 22179}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22183, "end": 22184}, "4": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22180, "end": 22182}, "5": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22163, "end": 22400}, "6": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22253, "end": 22257}, "7": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22248, "end": 22260}, "8": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22262, "end": 22266}, "9": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22262, "end": 22274}, "11": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22226, "end": 22275}, "12": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22294, "end": 22307}, "13": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22289, "end": 22291}, "14": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22332, "end": 22333}, "15": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22317, "end": 22321}, "16": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22317, "end": 22329}, "17": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22317, "end": 22333}, "18": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22367, "end": 22371}, "19": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22362, "end": 22374}, "20": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22376, "end": 22380}, "21": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22376, "end": 22388}, "23": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22390, "end": 22392}, "24": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22343, "end": 22393}, "25": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22487, "end": 22491}, "26": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22482, "end": 22494}, "27": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22504, "end": 22508}, "28": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22504, "end": 22516}, "30": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22447, "end": 22523}, "31": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22411, "end": 22416}, "32": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22537, "end": 22542}, "34": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22537, "end": 22565}, "35": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22569, "end": 22573}, "36": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22569, "end": 22581}, "38": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22566, "end": 22568}, "39": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22529, "end": 22602}, "43": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22583, "end": 22601}, "44": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22529, "end": 22602}, "45": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22608, "end": 22613}}, "is_native": false}, "44": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22745, "end": 22893}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22749, "end": 22772}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22773, "end": 22780}]], "returns": [{"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22804, "end": 22824}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22831, "end": 22838}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22831, "end": 22858}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 22831, "end": 22891}}, "is_native": false}, "45": {"location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 23051, "end": 23232}, "definition_location": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 23055, "end": 23085}, "type_parameters": [], "parameters": [["wrapper#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 23086, "end": 23093}], ["estimates_bytes#0#0", {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 23116, "end": 23131}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 23151, "end": 23158}, "1": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 23151, "end": 23182}, "2": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 23214, "end": 23229}, "3": {"file_hash": [162, 85, 120, 79, 199, 72, 62, 110, 180, 0, 128, 229, 73, 250, 88, 191, 157, 80, 254, 55, 71, 49, 9, 31, 143, 122, 98, 0, 92, 118, 41, 200], "start": 23151, "end": 23230}}, "is_native": false}}, "constant_map": {"ENotSystemAddress": 0, "EWrongInnerVersion": 1}}