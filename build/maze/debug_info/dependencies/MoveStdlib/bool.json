{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_2c930c25f8d3/crates/sui-framework/packages/move-stdlib/sources/bool.move", "definition_location": {"file_hash": [29, 198, 236, 217, 96, 2, 230, 248, 20, 163, 205, 217, 169, 95, 54, 36, 131, 201, 240, 95, 13, 180, 143, 59, 13, 67, 158, 109, 128, 195, 158, 80], "start": 114, "end": 118}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000001", "bool"], "struct_map": {}, "enum_map": {}, "function_map": {}, "constant_map": {}}