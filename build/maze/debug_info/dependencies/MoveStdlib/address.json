{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_2c930c25f8d3/crates/sui-framework/packages/move-stdlib/sources/address.move", "definition_location": {"file_hash": [215, 31, 27, 119, 207, 27, 244, 240, 122, 83, 187, 153, 168, 75, 241, 202, 106, 65, 128, 15, 29, 70, 148, 104, 172, 106, 9, 229, 195, 130, 218, 151], "start": 174, "end": 181}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000001", "address"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [215, 31, 27, 119, 207, 27, 244, 240, 122, 83, 187, 153, 168, 75, 241, 202, 106, 65, 128, 15, 29, 70, 148, 104, 172, 106, 9, 229, 195, 130, 218, 151], "start": 277, "end": 312}, "definition_location": {"file_hash": [215, 31, 27, 119, 207, 27, 244, 240, 122, 83, 187, 153, 168, 75, 241, 202, 106, 65, 128, 15, 29, 70, 148, 104, 172, 106, 9, 229, 195, 130, 218, 151], "start": 288, "end": 294}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [215, 31, 27, 119, 207, 27, 244, 240, 122, 83, 187, 153, 168, 75, 241, 202, 106, 65, 128, 15, 29, 70, 148, 104, 172, 106, 9, 229, 195, 130, 218, 151], "start": 298, "end": 301}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [215, 31, 27, 119, 207, 27, 244, 240, 122, 83, 187, 153, 168, 75, 241, 202, 106, 65, 128, 15, 29, 70, 148, 104, 172, 106, 9, 229, 195, 130, 218, 151], "start": 308, "end": 310}}, "is_native": false}}, "constant_map": {}}